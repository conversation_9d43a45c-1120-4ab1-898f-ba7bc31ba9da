@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 - 简化版本 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 修复水合不匹配：明确设置 overscroll 行为 */
  overscroll-behavior-x: none;
}

/* 矩阵专用样式 */

/* 矩阵视口（外层滚动容器） */
.matrix-viewport {
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.matrix-viewport::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.matrix-viewport::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

.matrix-viewport::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 6px;
  border: 2px solid #f1f5f9;
}

.matrix-viewport::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.matrix-viewport::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* 矩阵容器（内层矩阵） */
.matrix-container {
  will-change: transform;
  contain: layout style paint;
}

/* 矩阵单元格 */
.matrix-cell {
  transition: all 0.1s ease;
  box-sizing: border-box;
  user-select: none;
  cursor: pointer;
}

.matrix-cell:hover {
  transform: scale(1.05);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.matrix-cell.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.matrix-cell.coordinate-mode {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
}

.matrix-cell.color-mode {
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
  font-size: 12px;
}

.matrix-cell.level-mode {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  font-size: 14px;
}

.matrix-cell.word-mode {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.2;
  font-size: 12px;
}